package com.bxm.customer.domain.vo.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 社保信息VO
 * 
 * 用于表示员工的社保套餐信息，包含各种社保类型的参保状态。
 * 替代原有的JSON字符串存储方式，提供类型安全的对象操作。
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("社保信息VO")
public class SocialInsuranceVO {

    /** 养老保险 */
    @NotNull(message = "养老保险参保状态不能为空")
    @ApiModelProperty(value = "养老保险参保状态", required = true)
    private Boolean yangLao;

    /** 失业保险 */
    @NotNull(message = "失业保险参保状态不能为空")
    @ApiModelProperty(value = "失业保险参保状态", required = true)
    private Boolean shiYe;

    /** 工伤保险 */
    @NotNull(message = "工伤保险参保状态不能为空")
    @ApiModelProperty(value = "工伤保险参保状态", required = true)
    private Boolean gongShang;

    /** 医疗保险 */
    @NotNull(message = "医疗保险参保状态不能为空")
    @ApiModelProperty(value = "医疗保险参保状态", required = true)
    private Boolean yiLiao;

    /** 生育保险 */
    @NotNull(message = "生育保险参保状态不能为空")
    @ApiModelProperty(value = "生育保险参保状态", required = true)
    private Boolean shengYu;

    /** 其他保险 */
    @ApiModelProperty(value = "其他保险参保状态")
    private Boolean qiTa;

    /**
     * 验证社保信息的业务逻辑
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        // 基本字段不能为空
        if (yangLao == null || shiYe == null || gongShang == null || 
            yiLiao == null || shengYu == null) {
            return false;
        }
        
        // 至少需要参加一种保险
        return yangLao || shiYe || gongShang || yiLiao || shengYu || 
               (qiTa != null && qiTa);
    }

    /**
     * 获取验证错误信息
     * 
     * @return 错误信息，如果验证通过则返回null
     */
    public String getValidationMessage() {
        if (yangLao == null) {
            return "养老保险参保状态不能为空";
        }
        if (shiYe == null) {
            return "失业保险参保状态不能为空";
        }
        if (gongShang == null) {
            return "工伤保险参保状态不能为空";
        }
        if (yiLiao == null) {
            return "医疗保险参保状态不能为空";
        }
        if (shengYu == null) {
            return "生育保险参保状态不能为空";
        }
        
        // 检查是否至少参加一种保险
        if (!yangLao && !shiYe && !gongShang && !yiLiao && !shengYu && 
            (qiTa == null || !qiTa)) {
            return "至少需要参加一种社会保险";
        }
        
        return null;
    }

    /**
     * 创建默认的社保信息（全部参保）
     * 
     * @return 默认社保信息对象
     */
    public static SocialInsuranceVO createDefault() {
        return SocialInsuranceVO.builder()
                .yangLao(true)
                .shiYe(true)
                .gongShang(true)
                .yiLiao(true)
                .shengYu(true)
                .qiTa(false)
                .build();
    }

    /**
     * 创建空的社保信息（全部不参保）
     * 
     * @return 空社保信息对象
     */
    public static SocialInsuranceVO createEmpty() {
        return SocialInsuranceVO.builder()
                .yangLao(false)
                .shiYe(false)
                .gongShang(false)
                .yiLiao(false)
                .shengYu(false)
                .qiTa(false)
                .build();
    }

    /**
     * 从字符串值创建布尔值（用于Excel导入）
     * 
     * @param value 字符串值
     * @return 布尔值
     */
    public static Boolean convertFromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        
        String trimmed = value.trim();
        // 支持多种表示方式
        return "是".equals(trimmed) || "true".equalsIgnoreCase(trimmed) || 
               "1".equals(trimmed) || "参保".equals(trimmed) || "√".equals(trimmed);
    }

    /**
     * 转换为字符串值（用于Excel导出）
     * 
     * @param value 布尔值
     * @return 字符串表示
     */
    public static String convertToString(Boolean value) {
        if (value == null) {
            return "-";
        }
        return value ? "是" : "否";
    }
}
