package com.bxm.customer.domain.query.valueAdded;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增值员工信息查询参数
 *
 * 说明：所有查询条件均为可选，最终条件由 Service 层进行动态拼接
 * 支持按业务类型查询：1-社保明细，2-个税明细
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("增值员工信息查询参数")
public class EmployeeQuery extends BaseVO {

    @ApiModelProperty(value = "业务类型：1-社保明细，2-个税明细", required = true, allowableValues = "1,2")
    private Integer bizType;

}
