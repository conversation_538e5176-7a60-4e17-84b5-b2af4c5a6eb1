package com.bxm.customer.domain.handler;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import lombok.extern.slf4j.Slf4j;

/**
 * SocialInsuranceVO <-> JSON字符串 的MyBatis-Plus TypeHandler
 *
 * 继承 AbstractJsonTypeHandler 是MP提供的一种更便捷的方式来处理JSON
 * 使用FastJSON进行JSON序列化和反序列化处理
 * 
 * <AUTHOR>
 * @date 2025-01-30
 */
@Slf4j
@MappedTypes(SocialInsuranceVO.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class SocialInsuranceTypeHandler extends AbstractJsonTypeHandler<SocialInsuranceVO> {

    @Override
    protected SocialInsuranceVO parse(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                log.warn("Attempting to parse empty JSON string to SocialInsuranceVO");
                return null;
            }

            // 使用FastJSON解析JSON字符串为SocialInsuranceVO对象
            SocialInsuranceVO result = JSON.parseObject(json, SocialInsuranceVO.class);
            
            // 处理向后兼容性：如果qiTa字段不存在，设置为false
            if (result != null && result.getQiTa() == null) {
                result.setQiTa(false);
            }

            return result;
        } catch (JSONException e) {
            log.error("Failed to deserialize JSON to SocialInsuranceVO: {}", json, e);
            throw new RuntimeException("Unable to deserialize JSON to SocialInsuranceVO: " + e.getMessage(), e);
        }
    }

    @Override
    protected String toJson(SocialInsuranceVO obj) {
        try {
            if (obj == null) {
                return null;
            }

            // 在序列化前验证业务逻辑
            if (!obj.isValid()) {
                String errorMsg = obj.getValidationMessage();
                log.error("Attempting to serialize invalid SocialInsuranceVO: {}", errorMsg);
                throw new RuntimeException("Social insurance validation failed, cannot serialize: " + errorMsg);
            }

            // 确保qiTa字段有默认值
            if (obj.getQiTa() == null) {
                obj.setQiTa(false);
            }

            return JSON.toJSONString(obj);
        } catch (JSONException e) {
            log.error("Failed to serialize SocialInsuranceVO to JSON: {}", obj, e);
            throw new RuntimeException("Unable to serialize SocialInsuranceVO to JSON: " + e.getMessage(), e);
        }
    }
}
