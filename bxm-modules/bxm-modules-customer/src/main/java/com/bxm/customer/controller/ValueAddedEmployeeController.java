package com.bxm.customer.controller;

import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailExportDTO;
import com.bxm.customer.domain.dto.valueAdded.PersonalTaxDetailImportDTO;
import com.bxm.customer.domain.dto.valueAdded.SocialInsuranceDTO;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.query.valueAdded.ValueAddedEmployeeQuery;
import com.bxm.customer.domain.vo.valueAdded.BatchAddEmpRequest;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.service.IValueAddedFileService;
import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 增值员工信息Controller
 *
 * 提供增值员工信息的upsert操作，支持三种业务类型：
 * 1. 社医保（bizType=1）：支持提醒、更正、减员操作
 * 2. 个税明细（bizType=2）：支持提醒、更正、减员操作
 * 3. 国税账号（bizType=3）：支持会计实名、异地实名操作
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RestController
@RequestMapping("/valueAddedEmployee")
@Api(tags = "增值员工信息管理")
public class ValueAddedEmployeeController extends BaseController {

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    /**
     * 新增或更新增值员工信息
     */

    @Log(title = "增值员工信息", businessType = BusinessType.INSERT)
    @PostMapping("/upsert")
    @ApiOperation(value = "新增或更新增值员工信息", notes = "根据交付单ID+身份证号+业务类型判断是新增还是更新")
    public AjaxResult upsert(@Valid @RequestBody ValueAddedEmployeeVO employeeVO) {
        try {
            log.info("Received upsert request for employee: {}, bizType: {}",  employeeVO.getEmployeeName(), employeeVO.getBizType());

            boolean result = valueAddedEmployeeService.upsert(employeeVO);

            if (result) {
                return AjaxResult.success("操作成功");
            } else {
                return AjaxResult.error("操作失败");
            }
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in upsert request: {}", e.getMessage());
            return AjaxResult.error("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in upsert request for employee: {}", employeeVO.getEmployeeName(), e);
            return AjaxResult.error("操作失败：" + e.getMessage());
        }
    }
    /**
     * 根据交付单ID和身份证号查询员工信息
     */


    @GetMapping("/getByDeliveryOrderAndIdNumber")
    @ApiOperation(value = "根据交付单编号和身份证号查询员工信息", notes = "用于检查员工是否已存在")
    public AjaxResult getByDeliveryOrderAndIdNumber(
            @RequestParam("deliveryOrderNo") @ApiParam("交付单编号") String deliveryOrderNo,
            @RequestParam("idNumber") @ApiParam("身份证号") String idNumber,
            @RequestParam("bizType") @ApiParam("业务类型：1-社医保，2-个税明细，3-国税账号") Integer bizType) {
        try {
            ValueAddedEmployee employee = valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                    deliveryOrderNo, idNumber, bizType);

            if (employee != null) {
                return AjaxResult.success("查询成功", employee);
            } else {
                return AjaxResult.success("未找到匹配的员工信息", null);
            }
        } catch (Exception e) {
            log.error("Error querying employee by deliveryOrderNo: {}, idNumber: {}, bizType: {}", deliveryOrderNo, idNumber, bizType, e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询员工详情
     */
    @GetMapping("/getById/{id}")
    @ApiOperation(value = "根据ID查询员工详情", notes = "获取员工的完整信息")
    public AjaxResult getById(@PathVariable("id") @ApiParam("员工ID") Long id) {
        try {
            ValueAddedEmployee employee = valueAddedEmployeeService.getById(id);

            if (employee != null) {
                return AjaxResult.success("查询成功", employee);
            } else {
                return AjaxResult.error("未找到指定的员工信息");
            }
        } catch (Exception e) {
            log.error("Error querying employee by id: {}", id, e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出增值业务Excel模板
     */
    @GetMapping("/exportValueAddedEmptyExcelTemplate")
    @ApiOperation(value = "导出增值业务Excel模板", notes = "根据业务类型下载对应的空白模板")
    @Log(title = "增值业务模板导出", businessType = BusinessType.EXPORT)
    public void exportValueAddedEmptyExcelTemplate(
            @RequestParam("bizType") @ApiParam(value = "业务类型：1-社医保，2-个税明细", required = true, allowableValues = "1,2") Integer bizType,
            HttpServletResponse response) {
        log.info("Starting export value added template, bizType: {}", bizType);

        // 验证业务类型参数
        if (bizType == null || (bizType != 1 && bizType != 2)) {
            throw new IllegalArgumentException("业务类型参数无效，必须为1（社医保）或2（个税明细）");
        }

        try {
            if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
                // 社医保模板
                ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
                util.importTemplateExcel(response, "社保明细");
                log.info("Successfully exported social insurance detail template");
            } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
                // 个税明细模板
                ExcelUtil<PersonalTaxDetailImportDTO> util = new ExcelUtil<>(PersonalTaxDetailImportDTO.class);
                util.importTemplateExcel(response, "个税明细");
                log.info("Successfully exported personal tax detail template");
            }
        } catch (Exception e) {
            log.error("Failed to export value added template, bizType: {}", bizType, e);
            throw new RuntimeException("导出模板失败: " + e.getMessage());
        }
    }

    /**
     * 批量新增增值员工信息（支持社医保和个税明细）
     */
    @PostMapping("/batchSaveValueAddedEmp")
    @ApiOperation(value = "批量新增增值员工信息", notes = "上传Excel文件和表单数据，支持社医保和个税明细，立即返回文件ID，后台异步处理")
    @Log(title = "批量新增增值员工", businessType = BusinessType.INSERT)
    public AjaxResult batchSaveValueAddedEmp(
            @ModelAttribute @Valid BatchAddEmpRequest request,
            @RequestParam("excelFile") @ApiParam("Excel文件") MultipartFile excelFile) {
        try {
            log.info("Received batch add value added employees request, fileName: {}, bizType: {}",excelFile.getOriginalFilename(), request.getBizType());

            // 基础验证
            if (excelFile == null || excelFile.isEmpty()) {
                return AjaxResult.error("Excel文件不能为空");
            }

            // 1. 保存文件到c_value_added_file表
            Long fileId = valueAddedFileService.savePersonnelExcelFile(excelFile, request.getDeliveryOrderNo());

            // 2. 异步处理Excel数据
            valueAddedEmployeeService.processBatchEmployeeData(
                    fileId,
                    request.getBizType(),
                    request.getOverrideExisting()
            );

            return AjaxResult.success("文件上传成功，正在后台处理", fileId);
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in batch add request: {}", e.getMessage());
            return AjaxResult.error("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in batch add request", e);
            return AjaxResult.error("批量处理启动失败：" + e.getMessage());
        }
    }

    /**
     * 查询文件处理进度
     */
    @GetMapping("/fileProgress/{fileId}")
    @ApiOperation(value = "查询文件处理进度", notes = "根据文件ID查询处理进度和结果")
    public AjaxResult getFileProgress(@PathVariable("fileId") @ApiParam("文件ID") Long fileId) {
        try {
            log.info("Query file progress, fileId: {}", fileId);

            if (fileId == null) {
                return AjaxResult.error("文件ID不能为空");
            }

            String statusJson = valueAddedEmployeeService.getProcessStatusByFileId(fileId);

            //
            @SuppressWarnings("unchecked")
            Map<String, Object> statusMap = JSON.parseObject(statusJson, Map.class);

            return AjaxResult.success("查询成功", statusMap);
        } catch (Exception e) {
            log.error("Error querying file progress, fileId: {}", fileId, e);
            return AjaxResult.error("查询进度失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询增值员工信息
     */
    @GetMapping("/query")
    @ApiOperation(value = "分页查询增值员工信息", notes = "根据业务类型等条件分页查询员工信息，支持社保明细和个税明细")
    @Log(title = "Query value added employees", businessType = BusinessType.OTHER)
    public Object query(ValueAddedEmployeeQuery query) {
        try {
            // 验证必填参数
            if (query.getBizType() == null) {
                throw new IllegalArgumentException("业务类型不能为空");
            }

            // 验证业务类型范围（只支持社保明细和个税明细）
            if (query.getBizType() != ValueAddedBizType.SOCIAL_INSURANCE.getCode()
                && query.getBizType() != ValueAddedBizType.PERSONAL_TAX.getCode()) {
                throw new IllegalArgumentException("业务类型只支持1（社保明细）或2（个税明细）");
            }

            startPage();

            // 记录查询关键信息
            log.info("Query value added employees, bizType={}, deliveryOrderNo={}, employeeName={}",
                    query.getBizType(),
                    com.bxm.common.core.utils.StringUtils.nvl(query.getDeliveryOrderNo(), ""),
                    com.bxm.common.core.utils.StringUtils.nvl(query.getEmployeeName(), ""));

            // 执行查询
            List<ValueAddedEmployeeVO> list = valueAddedEmployeeService.query(query);

            return getDataTable(list);
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in query request: {}", e.getMessage());
            return AjaxResult.error("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Error querying value added employees", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

}
